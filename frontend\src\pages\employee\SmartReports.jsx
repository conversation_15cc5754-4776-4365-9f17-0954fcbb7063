import React, { useState } from 'react';
import { 
  DocumentTextIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  UserIcon,
  EyeIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '@/hooks/useAuth';
import ReportsList from '@/components/smart-reports/ReportsList';
import ReportViewer from '@/components/smart-reports/ReportViewer';

/**
 * Employee Smart Reports Page
 * View-only access to reports generated about the employee
 */
const EmployeeSmartReports = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('list');
  const [selectedReport, setSelectedReport] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  /**
   * Handle viewing a specific report
   */
  const handleViewReport = (report) => {
    setSelectedReport(report);
    setActiveTab('view');
  };

  /**
   * <PERSON>le going back from report view
   */
  const handleBackFromReport = () => {
    setSelectedReport(null);
    setActiveTab('list');
  };

  /**
   * Handle export report
   */
  const handleExportReport = (report) => {
    // Implement export functionality for employees
    console.log('Exporting report:', report);
  };

  /**
   * Get tab configuration for employees
   */
  const getTabConfig = () => [
    {
      key: 'list',
      label: 'My Reports',
      icon: DocumentTextIcon,
      description: 'View performance reports about you'
    },
    ...(selectedReport ? [{
      key: 'view',
      label: 'View Report',
      icon: ChartBarIcon,
      description: selectedReport.reportName
    }] : [])
  ];

  /**
   * Render tab navigation
   */
  const renderTabNavigation = () => {
    const tabs = getTabConfig();

    return (
      <div className="border-b border-gray-200 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`
                    group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                    transition-all duration-200 ease-in-out
                    ${activeTab === tab.key
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <IconComponent className={`
                    -ml-0.5 mr-2 h-5 w-5
                    ${activeTab === tab.key ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'}
                  `} />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>
    );
  };

  /**
   * Render page header with employee-specific info
   */
  const renderPageHeader = () => (
    <div className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <EyeIcon className="h-8 w-8 text-indigo-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                  My Performance Reports
                </h1>
                <p className="mt-1 text-sm text-gray-500">
                  AI-generated insights about your performance and development
                </p>
              </div>
            </div>
          </div>
          
          <div className="mt-4 flex md:mt-0 md:ml-4">
            {/* Employee Quick Stats */}
            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">View Only</div>
                <div>Access Level</div>
              </div>
            </div>
          </div>
        </div>

        {/* Employee Info Banner */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <InformationCircleIcon className="h-5 w-5 text-blue-600 mr-2" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-blue-900">About Your Reports</h3>
              <p className="text-sm text-blue-700 mt-1">
                These reports are generated by your manager or HR team to provide insights into your performance, 
                achievements, and areas for development. Use them to understand your progress and plan your growth.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  /**
   * Render employee guidance section
   */
  const renderEmployeeGuidance = () => (
    <div className="mb-6 space-y-4">
      {/* What are Smart Reports */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start">
          <UserIcon className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
          <div>
            <h3 className="text-sm font-medium text-green-900">What are Smart Reports?</h3>
            <p className="text-sm text-green-700 mt-1">
              Smart Reports use AI to analyze your performance data and provide natural language summaries 
              of your achievements, strengths, and areas for improvement. They help you understand your 
              performance trends and get actionable recommendations.
            </p>
          </div>
        </div>
      </div>

      {/* How to use reports */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-start">
          <ChartBarIcon className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
          <div>
            <h3 className="text-sm font-medium text-purple-900">How to Use Your Reports</h3>
            <div className="text-sm text-purple-700 mt-1">
              <ul className="list-disc list-inside space-y-1">
                <li>Review the AI-generated summary for overall performance insights</li>
                <li>Check the insights section for specific strengths and achievements</li>
                <li>Read recommendations for your professional development</li>
                <li>Use the data snapshot to see the metrics behind the analysis</li>
                <li>Discuss the findings with your manager during one-on-ones</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  /**
   * Render active tab content
   */
  const renderTabContent = () => {
    switch (activeTab) {
      case 'view':
        return (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ReportViewer
              report={selectedReport}
              onBack={handleBackFromReport}
              onExport={handleExportReport}
              // Note: No share functionality for employees
            />
          </div>
        );

      case 'list':
      default:
        return (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Employee Guidance */}
            {renderEmployeeGuidance()}

            {/* Reports List - Employee view only shows their own reports */}
            <ReportsList
              onViewReport={handleViewReport}
              // Note: No generate new functionality for employees
              refreshTrigger={refreshTrigger}
            />
          </div>
        );
    }
  };

  /**
   * Render no reports state for employees
   */
  const renderNoReportsState = () => (
    <div className="text-center py-12">
      <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
      <h3 className="mt-4 text-lg font-medium text-gray-900">No Reports Available</h3>
      <p className="mt-2 text-sm text-gray-500 max-w-sm mx-auto">
        Your manager or HR team hasn't generated any performance reports for you yet. 
        Reports will appear here once they're created.
      </p>
      <div className="mt-6">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-w-md mx-auto">
          <h4 className="text-sm font-medium text-gray-900 mb-2">What you can do:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Ask your manager about performance reviews</li>
            <li>• Ensure your goals and achievements are up to date</li>
            <li>• Maintain good attendance and performance records</li>
          </ul>
        </div>
      </div>
    </div>
  );

  // Check if user has employee access
  if (user?.role !== 'employee') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">Access Denied</h3>
          <p className="mt-2 text-sm text-gray-500">
            You don't have permission to access the employee smart reports page.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      {renderPageHeader()}

      {/* Tab Navigation */}
      {renderTabNavigation()}

      {/* Tab Content */}
      <div className="bg-gray-50 min-h-screen">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default EmployeeSmartReports;
