1. Understand the product in depth. Relate it to the AI features which have been asked. Focus on planning most important features and ask chatgpt. 
2. Then dedicatedly focus on AI features. understand them in depth. brain storm and plan their frontend backend database, vector database imlementation.
3. Plan the UI flow of the product.
4. Plan database schema and vector database schema.
5. Plan backend design of all APIs, make sure all goals from PRD satisfied and improve if any logical issue
6. Plan the UI screens and backend API relation and flow.