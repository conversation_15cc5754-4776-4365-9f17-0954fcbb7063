-- Migration: Add ai_smart_reports table for Smart Reports feature
-- This table stores AI-generated natural language summaries of employee/team performance

CREATE TABLE ai_smart_reports (
  id INT AUTO_INCREMENT PRIMARY KEY,
  report_type ENUM('employee', 'team', 'department') NOT NULL,
  target_id INT NOT NULL, -- employee_id for employee reports, manager_id for team reports
  report_name VARCHAR(200) NOT NULL,
  ai_summary TEXT NOT NULL, -- Natural language summary generated by AI
  insights_json TEXT, -- Key insights as JSON array
  recommendations_json TEXT, -- Recommendations as JSON array
  data_snapshot_json TEXT, -- Raw data used for generation (for reference)
  generated_by INT NOT NULL, -- user_id who generated the report
  status ENUM('generating', 'completed', 'failed') DEFAULT 'generating',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Foreign key constraints
  FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE CASCADE,
  
  -- Indexes for performance
  INDEX idx_report_type (report_type),
  INDEX idx_target_id (target_id),
  INDEX idx_generated_by (generated_by),
  INDEX idx_created_at (created_at),
  INDEX idx_status (status)
);

-- Add some sample data for testing (optional)
-- INSERT INTO ai_smart_reports (report_type, target_id, report_name, ai_summary, insights_json, recommendations_json, data_snapshot_json, generated_by, status) 
-- VALUES ('employee', 2, 'Employee Performance Report - Manager Smith', 'Sample AI summary...', '[]', '[]', '{}', 1, 'completed');
