# =============================================
# HRMS Backend Environment Configuration
# =============================================
# Copy this file to .env and update with your actual values
# Never commit .env file to version control

# =============================================
# SERVER CONFIGURATION
# =============================================
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000

# =============================================
# DATABASE CONFIGURATION (MySQL)
# =============================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=hrms_db
DB_USER=root
DB_PASSWORD=your_mysql_password_here

# =============================================
# JWT AUTHENTICATION
# =============================================
# Generate strong secrets using: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
JWT_SECRET=your_super_secret_jwt_key_here_make_it_at_least_64_characters_long
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_refresh_token_secret_here_also_make_it_very_long
JWT_REFRESH_EXPIRES_IN=7d

# =============================================
# AI SERVICE CONFIGURATION (Gemini API)
# =============================================
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_from_google_ai_studio
GEMINI_MODEL=gemini-2.0-flash
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_TOKENS=1000

# =============================================
# EMAIL CONFIGURATION (Optional)
# =============================================
# Required only for email notifications
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_gmail_app_password

# =============================================
# FILE UPLOAD CONFIGURATION
# =============================================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png

# =============================================
# SECURITY CONFIGURATION
# =============================================
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:3000

# =============================================
# LOGGING CONFIGURATION
# =============================================
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# =============================================
# AI FEATURES CONFIGURATION
# =============================================
# Resume Parser Settings
RESUME_PARSER_MAX_SIZE=5242880
RESUME_PARSER_TIMEOUT=30000

# Attrition Predictor Settings
ATTRITION_PREDICTION_THRESHOLD=0.7
ATTRITION_UPDATE_FREQUENCY=daily

# Chatbot Settings
CHATBOT_SESSION_TIMEOUT=1800000
CHATBOT_MAX_HISTORY=10

# Anomaly Detection Settings
ANOMALY_DETECTION_ENABLED=true
ANOMALY_CHECK_FREQUENCY=daily

# Smart Reports Settings
REPORTS_CACHE_TTL=3600000
REPORTS_MAX_RECORDS=10000
